using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Security.Claims;
using Volo.Abp.Users;

namespace Imip.JettyApproval.Web.Authorization.Permissions;

public class IdentityServerCurrentUserApplicationConfigurationContributor : IApplicationConfigurationContributor, ITransientDependency
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<IdentityServerCurrentUserApplicationConfigurationContributor> _logger;

    public IdentityServerCurrentUserApplicationConfigurationContributor(
        IHttpContextAccessor httpContextAccessor,
        ILogger<IdentityServerCurrentUserApplicationConfigurationContributor> logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    public async Task ContributeAsync(ApplicationConfigurationContributorContext context)
    {
        await ConfigureCurrentUserAsync(context);
    }

    private async Task ConfigureCurrentUserAsync(ApplicationConfigurationContributorContext context)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.User?.Identity?.IsAuthenticated != true)
        {
            return;
        }

        var principal = httpContext.User;
        
        // Get roles from claims principal instead of database
        var roleClaims = principal.Claims
            .Where(c => c.Type == ClaimTypes.Role)
            .Select(c => c.Value)
            .ToArray();

        if (roleClaims.Length > 0)
        {
            _logger.LogInformation("Found {Count} roles in claims principal for current user: [{Roles}]", 
                roleClaims.Length, string.Join(", ", roleClaims));

            // Update the current user configuration with roles from claims
            if (context.ApplicationConfiguration.CurrentUser != null)
            {
                context.ApplicationConfiguration.CurrentUser.Roles = roleClaims;
                _logger.LogInformation("Updated application configuration currentUser.roles with {Count} roles", roleClaims.Length);
            }
        }
        else
        {
            _logger.LogWarning("No role claims found in claims principal for current user");
        }

        await Task.CompletedTask;
    }
}
