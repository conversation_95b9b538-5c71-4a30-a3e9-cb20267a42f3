using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Volo.Abp.Guids;
using Volo.Abp.Identity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Security.Claims;
using Volo.Abp.Data;

namespace Imip.JettyApproval.Web.Services;

public class AuthenticationTokenValidationService : IAuthenticationTokenValidationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<AuthenticationTokenValidationService> _logger;
    private readonly IdentityUserManager _userManager;
    private readonly ICurrentTenant _currentTenant;
    private readonly IGuidGenerator _guidGenerator;

    public AuthenticationTokenValidationService(
        IConfiguration configuration,
        ILogger<AuthenticationTokenValidationService> logger,
        IdentityUserManager userManager,
        ICurrentTenant currentTenant,
        IGuidGenerator guidGenerator)
    {
        _configuration = configuration;
        _logger = logger;
        _userManager = userManager;
        _currentTenant = currentTenant;
        _guidGenerator = guidGenerator;
    }

    public async Task ValidateJwtBearerTokenAsync(Microsoft.AspNetCore.Authentication.JwtBearer.TokenValidatedContext context)
    {
        try
        {
            var claimsPrincipal = context.Principal;
            var email = GetEmailFromClaims(claimsPrincipal);
            var name = GetNameFromClaims(claimsPrincipal);
            var userName = GetUserNameFromClaims(claimsPrincipal);
            var sub = GetSubjectFromClaims(claimsPrincipal);

            if (string.IsNullOrEmpty(email))
            {
                _logger.LogWarning("JWT token validated but no email claim found");
                context.Fail("No email claim found in token");
                return;
            }

            // Find or create user in our system
            var user = await FindOrCreateUserAsync(email, name, userName, sub);
            if (user != null && claimsPrincipal.Identity is ClaimsIdentity identity)
            {
                // Add ABP-specific claims to the existing principal
                AddAbpClaims(identity, user);

                _logger.LogInformation("JWT token validated successfully for user: {Email}", email);
            }
            else
            {
                _logger.LogWarning("JWT token validated but user not found in local system: {Email}", email);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing JWT token validation");
            context.Fail("Token validation failed");
        }
    }

    public async Task ValidateOpenIdConnectTokenAsync(Microsoft.AspNetCore.Authentication.OpenIdConnect.TokenValidatedContext context)
    {
        try
        {
            var serviceProvider = context.HttpContext.RequestServices;
            var claimsPrincipal = context.Principal;

            // Extract claims BEFORE any transformations
            var email = GetEmailFromClaims(claimsPrincipal);
            var userName = GetUserNameFromClaims(claimsPrincipal);
            var sub = GetSubjectFromClaims(claimsPrincipal);

            // For name, try to get it from the ID token directly first
            var name = GetNameFromIdToken(context) ?? GetNameFromClaims(claimsPrincipal);

            // Extract roles from token and claims
            var roles = GetRolesFromTokenAndClaims(context, claimsPrincipal);

            _logger.LogInformation("OIDC Token Claims - Email: {Email}, Name: {Name}, UserName: {UserName}, Sub: {Sub}, Roles: [{Roles}]",
                email, name, userName, sub, string.Join(", ", roles));

            if (string.IsNullOrEmpty(email))
            {
                _logger.LogWarning("OIDC token validated but no email claim found");
                context.Fail("No email claim found in token");
                return;
            }

            // Find or create user in our system
            var user = await FindOrCreateUserAsync(email, name, userName, sub, roles);
            if (user != null && claimsPrincipal.Identity is ClaimsIdentity identity)
            {
                // Add ABP-specific claims to the existing principal
                AddAbpClaims(identity, user, roles);

                // Store tokens for later use
                await StoreTokensAsync(context, user, email);

                _logger.LogInformation("OIDC token validated successfully for user: {Email}, Name: {Name}, UserName: {UserName}",
                    email, user.Name, user.UserName);
            }
            else
            {
                _logger.LogError("Failed to find or create user for email: {Email}", email);
                context.Fail("User creation failed");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing OIDC token validation");
            context.Fail("Token validation failed");
        }
    }

    private string? GetNameFromIdToken(Microsoft.AspNetCore.Authentication.OpenIdConnect.TokenValidatedContext context)
    {
        try
        {
            // Try to get the name from the ID token directly
            if (context.SecurityToken is System.IdentityModel.Tokens.Jwt.JwtSecurityToken idToken)
            {
                var givenNameClaim = idToken.Claims.FirstOrDefault(c => c.Type == "given_name")?.Value;
                if (!string.IsNullOrEmpty(givenNameClaim))
                {
                    _logger.LogInformation("Found given_name in ID token: {GivenName}", givenNameClaim);
                    return givenNameClaim;
                }

                var nameClaim = idToken.Claims.FirstOrDefault(c => c.Type == "name")?.Value;
                if (!string.IsNullOrEmpty(nameClaim))
                {
                    _logger.LogInformation("Found name in ID token: {Name}", nameClaim);
                    return nameClaim;
                }
            }

            // Fallback: try to parse the access token if available
            if (context.TokenEndpointResponse?.AccessToken != null)
            {
                var tokenHandler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
                var accessToken = tokenHandler.ReadJwtToken(context.TokenEndpointResponse.AccessToken);

                var givenNameClaim = accessToken.Claims.FirstOrDefault(c => c.Type == "given_name")?.Value;
                if (!string.IsNullOrEmpty(givenNameClaim))
                {
                    _logger.LogInformation("Found given_name in access token: {GivenName}", givenNameClaim);
                    return givenNameClaim;
                }

                var nameClaim = accessToken.Claims.FirstOrDefault(c => c.Type == "name")?.Value;
                if (!string.IsNullOrEmpty(nameClaim))
                {
                    _logger.LogInformation("Found name in access token: {Name}", nameClaim);
                    return nameClaim;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract name from tokens");
        }

        return null;
    }

    private List<string> GetRolesFromTokenAndClaims(Microsoft.AspNetCore.Authentication.OpenIdConnect.TokenValidatedContext context, ClaimsPrincipal? claimsPrincipal)
    {
        var roles = new List<string>();

        try
        {
            // First, try to get roles from the ID token
            if (context.SecurityToken is System.IdentityModel.Tokens.Jwt.JwtSecurityToken idToken)
            {
                var tokenRoles = idToken.Claims.Where(c => c.Type == "role" || c.Type == ClaimTypes.Role).Select(c => c.Value).ToList();
                roles.AddRange(tokenRoles);
                if (tokenRoles.Any())
                {
                    _logger.LogInformation("Found {Count} roles in ID token: [{Roles}]", tokenRoles.Count, string.Join(", ", tokenRoles));
                }
            }

            // Then try to get roles from the access token
            if (context.TokenEndpointResponse?.AccessToken != null)
            {
                var tokenHandler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
                var accessToken = tokenHandler.ReadJwtToken(context.TokenEndpointResponse.AccessToken);

                var accessTokenRoles = accessToken.Claims.Where(c => c.Type == "role" || c.Type == ClaimTypes.Role).Select(c => c.Value).ToList();
                foreach (var role in accessTokenRoles)
                {
                    if (!roles.Contains(role))
                    {
                        roles.Add(role);
                    }
                }
                if (accessTokenRoles.Any())
                {
                    _logger.LogInformation("Found {Count} roles in access token: [{Roles}]", accessTokenRoles.Count, string.Join(", ", accessTokenRoles));
                }
            }

            // Finally, get roles from the claims principal
            if (claimsPrincipal != null)
            {
                var principalRoles = claimsPrincipal.Claims.Where(c => c.Type == "role" || c.Type == ClaimTypes.Role).Select(c => c.Value).ToList();
                foreach (var role in principalRoles)
                {
                    if (!roles.Contains(role))
                    {
                        roles.Add(role);
                    }
                }
                if (principalRoles.Any())
                {
                    _logger.LogInformation("Found {Count} roles in claims principal: [{Roles}]", principalRoles.Count, string.Join(", ", principalRoles));
                }
            }

            if (!roles.Any())
            {
                _logger.LogWarning("No roles found in tokens or claims principal");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to extract roles from tokens and claims");
        }

        return roles.Distinct().ToList();
    }

    private string? GetEmailFromClaims(ClaimsPrincipal? principal)
    {
        return principal?.FindFirst("email")?.Value ??
               principal?.FindFirst(ClaimTypes.Email)?.Value;
    }

    private string? GetNameFromClaims(ClaimsPrincipal? principal)
    {
        // Log all available claims for debugging
        if (principal != null)
        {
            _logger.LogInformation("Available claims for name extraction:");
            foreach (var claim in principal.Claims.Where(c => c.Type.Contains("name", StringComparison.OrdinalIgnoreCase) ||
                                                              c.Type.Contains("given", StringComparison.OrdinalIgnoreCase) ||
                                                              c.Type.Contains("family", StringComparison.OrdinalIgnoreCase)))
            {
                _logger.LogInformation("Claim: {Type} = {Value}", claim.Type, claim.Value);
            }

            // Also log all claims to see what's available
            _logger.LogInformation("All claims in principal:");
            foreach (var claim in principal.Claims)
            {
                _logger.LogInformation("All Claim: {Type} = {Value}", claim.Type, claim.Value);
            }
        }

        // First priority: given_name claim (this should contain the full name)
        var givenName = principal?.Claims.FirstOrDefault(c => c.Type == "given_name")?.Value;
        if (!string.IsNullOrEmpty(givenName))
        {
            _logger.LogInformation("Using given_name claim for name: {Name}", givenName);
            return givenName;
        }

        // Second priority: name claim (but not ClaimTypes.Name which might be username)
        var nameClaimValue = principal?.Claims.FirstOrDefault(c => c.Type == "name")?.Value;
        if (!string.IsNullOrEmpty(nameClaimValue))
        {
            _logger.LogInformation("Using name claim for name: {Name}", nameClaimValue);
            return nameClaimValue;
        }

        // Third priority: family_name
        var familyName = principal?.Claims.FirstOrDefault(c => c.Type == "family_name")?.Value;
        if (!string.IsNullOrEmpty(familyName))
        {
            _logger.LogInformation("Using family_name claim for name: {Name}", familyName);
            return familyName;
        }

        // Last resort - ClaimTypes.Name (but this might contain username, so be careful)
        var claimTypesName = principal?.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value;
        if (!string.IsNullOrEmpty(claimTypesName))
        {
            _logger.LogWarning("Using ClaimTypes.Name claim for name (might be username): {Name}", claimTypesName);
            return claimTypesName;
        }

        _logger.LogWarning("No suitable name claim found");
        return null;
    }

    private string? GetUserNameFromClaims(ClaimsPrincipal? principal)
    {
        return principal?.FindFirst("preferred_username")?.Value ??
               principal?.FindFirst("unique_name")?.Value ??
               principal?.FindFirst("username")?.Value;
    }

    private string? GetSubjectFromClaims(ClaimsPrincipal? principal)
    {
        return principal?.FindFirst("sub")?.Value ??
               principal?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }

    private void AddAbpClaims(ClaimsIdentity identity, IdentityUser user, List<string>? roles = null)
    {
        // Remove existing ABP claims to avoid duplicates
        var existingAbpClaims = identity.Claims
            .Where(c => c.Type == AbpClaimTypes.UserId ||
                       c.Type == AbpClaimTypes.UserName ||
                       c.Type == AbpClaimTypes.Email ||
                       c.Type == AbpClaimTypes.Name ||
                       c.Type == AbpClaimTypes.SurName ||
                       c.Type == AbpClaimTypes.TenantId ||
                       c.Type == ClaimTypes.Role)
            .ToList();

        foreach (var claim in existingAbpClaims)
        {
            identity.RemoveClaim(claim);
        }

        // Add ABP-specific claims
        identity.AddClaim(new Claim(AbpClaimTypes.UserId, user.Id.ToString()));
        identity.AddClaim(new Claim(AbpClaimTypes.UserName, user.UserName));
        identity.AddClaim(new Claim(AbpClaimTypes.Email, user.Email));

        if (!string.IsNullOrEmpty(user.Name))
        {
            identity.AddClaim(new Claim(AbpClaimTypes.Name, user.Name));
        }

        if (!string.IsNullOrEmpty(user.Surname))
        {
            identity.AddClaim(new Claim(AbpClaimTypes.SurName, user.Surname));
        }

        if (user.TenantId.HasValue)
        {
            identity.AddClaim(new Claim(AbpClaimTypes.TenantId, user.TenantId.Value.ToString()));
        }

        // Add roles from the provided list (from tokens) or fallback to existing claims
        if (roles != null && roles.Any())
        {
            foreach (var role in roles)
            {
                if (!string.IsNullOrEmpty(role) && !identity.HasClaim(c => c.Type == ClaimTypes.Role && c.Value == role))
                {
                    identity.AddClaim(new Claim(ClaimTypes.Role, role));
                    _logger.LogInformation("Added role claim: {Role}", role);
                }
            }
        }
        else
        {
            // Fallback: Add roles from the original token claims
            var roleClaims = identity.Claims.Where(c => c.Type == "role").ToList();
            foreach (var roleClaim in roleClaims)
            {
                if (!identity.HasClaim(c => c.Type == ClaimTypes.Role && c.Value == roleClaim.Value))
                {
                    identity.AddClaim(new Claim(ClaimTypes.Role, roleClaim.Value));
                }
            }
        }
    }

    private async Task<IdentityUser?> FindOrCreateUserAsync(string email, string? name, string? userName, string? sub, List<string>? roles = null)
    {
        _logger.LogInformation("FindOrCreateUserAsync - Email: {Email}, Name: {Name}, UserName: {UserName}, Sub: {Sub}, Roles: [{Roles}]",
            email, name, userName, sub, roles != null ? string.Join(", ", roles) : "none");

        // First try to find by external user ID (sub)
        var user = await _userManager.FindByIdAsync(sub);
        if (user != null)
        {
            _logger.LogInformation("Found existing user - Current Name: {CurrentName}, Current UserName: {CurrentUserName}",
                user.Name, user.UserName);

            // Update existing user information if needed
            var needsUpdate = false;

            if (!string.IsNullOrEmpty(name) && user.Name != name)
            {
                _logger.LogInformation("Updating user name from '{OldName}' to '{NewName}'", user.Name, name);
                user.Name = name;
                user.SetProperty("DisplayName", name);
                needsUpdate = true;
            }
            else if (!string.IsNullOrEmpty(name))
            {
                _logger.LogInformation("User name is already correct: '{Name}'", name);
            }
            else
            {
                _logger.LogWarning("No name provided for user update");
            }

            if (!string.IsNullOrEmpty(userName) && user.UserName != userName)
            {
                _logger.LogInformation("Updating user username from '{OldUserName}' to '{NewUserName}'", user.UserName, userName);
                await _userManager.SetUserNameAsync(user, userName);
                needsUpdate = true;
            }

            // Update external user ID if it changed
            var currentExternalId = user.GetProperty<string>("ExternalUserId");
            if (!string.IsNullOrEmpty(sub) && currentExternalId != sub)
            {
                user.SetProperty("ExternalUserId", sub);
                needsUpdate = true;
            }

            if (needsUpdate)
            {
                await _userManager.UpdateAsync(user);
                _logger.LogInformation("User updated successfully - Final Name: {Name}, UserName: {UserName}", user.Name, user.UserName);
            }
            else
            {
                _logger.LogInformation("No user updates needed - Current Name: {Name}, UserName: {UserName}", user.Name, user.UserName);
            }

            // Sync roles from identity server to local database
            if (roles != null && roles.Count > 0)
            {
                await SyncUserRolesAsync(user, roles);
            }

            return user;
        }

        // If not found, create new user
        try
        {
            _logger.LogInformation("Creating new user for email: {Email}, external ID: {ExternalId}, name: {Name}, userName: {UserName}",
                email, sub, name, userName);

            // Parse userId string to Guid
            if (!string.IsNullOrEmpty(sub) && Guid.TryParse(sub, out var userGuid))
            {
                // Use the external user ID as the internal ID
                user = new IdentityUser(
                    userGuid,
                    userName ?? email,
                    email,
                    tenantId: _currentTenant.Id
                );
            }
            else
            {
                // Generate a new ID if external ID is not a valid GUID
                user = new IdentityUser(
                    _guidGenerator.Create(),
                    userName ?? email,
                    email,
                    tenantId: _currentTenant.Id
                );
            }

            if (!string.IsNullOrEmpty(name))
            {
                _logger.LogInformation("Setting user name to: {Name}", name);
                user.Name = name;
                user.SetProperty("DisplayName", name);
            }
            else
            {
                _logger.LogWarning("No name provided for new user");
            }

            // Store the external user ID for future reference
            if (!string.IsNullOrEmpty(sub))
            {
                user.SetProperty("ExternalUserId", sub);
            }

            user.SetEmailConfirmed(true); // Since it's from SSO, consider email verified

            var result = await _userManager.CreateAsync(user);
            if (result.Succeeded)
            {
                _logger.LogInformation("Created new user from SSO: {Email}, Name: {Name}, UserName: {UserName}",
                    email, user.Name, user.UserName);
                return user;
            }
            else
            {
                _logger.LogError("Failed to create user {Email}: {Errors}",
                    email, string.Join(", ", result.Errors.Select(e => e.Description)));
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception creating user {Email}", email);
            return null;
        }
    }

    private async Task StoreTokensAsync(Microsoft.AspNetCore.Authentication.OpenIdConnect.TokenValidatedContext context, IdentityUser user, string email)
    {
        try
        {
            // Store tokens for later use
            if (context.Properties?.Items.ContainsKey(".Token.access_token") == true)
            {
                var accessToken = context.Properties.Items[".Token.access_token"];
                var refreshToken = context.Properties.Items.ContainsKey(".Token.refresh_token")
                    ? context.Properties.Items[".Token.refresh_token"]
                    : null;

                _logger.LogInformation("Access token stored for user: {Email}", email);

                // Store tokens in user properties for later use
                user.SetProperty("AccessToken", accessToken);
                if (!string.IsNullOrEmpty(refreshToken))
                {
                    user.SetProperty("RefreshToken", refreshToken);
                }

                // Update user with token information
                await _userManager.UpdateAsync(user);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing tokens for user: {Email}", email);
            // Don't fail the authentication if token storage fails
        }
    }

    private async Task SyncUserRolesAsync(IdentityUser user, List<string> newRoles)
    {
        try
        {
            _logger.LogInformation("Syncing roles for user {UserId}. New roles: [{Roles}]", user.Id, string.Join(", ", newRoles));

            // Get current roles from database
            var currentRoles = await _userManager.GetRolesAsync(user);
            _logger.LogInformation("Current roles in database: [{CurrentRoles}]", string.Join(", ", currentRoles));

            // Find roles to add and remove
            var rolesToAdd = newRoles.Except(currentRoles, StringComparer.OrdinalIgnoreCase).ToList();
            var rolesToRemove = currentRoles.Except(newRoles, StringComparer.OrdinalIgnoreCase).ToList();

            _logger.LogInformation("Roles to add: [{RolesToAdd}], Roles to remove: [{RolesToRemove}]",
                string.Join(", ", rolesToAdd), string.Join(", ", rolesToRemove));

            // Remove roles that are no longer present
            if (rolesToRemove.Count > 0)
            {
                var removeResult = await _userManager.RemoveFromRolesAsync(user, rolesToRemove);
                if (removeResult.Succeeded)
                {
                    _logger.LogInformation("Successfully removed {Count} roles from user {UserId}", rolesToRemove.Count, user.Id);
                }
                else
                {
                    _logger.LogWarning("Failed to remove roles from user {UserId}: {Errors}", user.Id,
                        string.Join(", ", removeResult.Errors.Select(e => e.Description)));
                }
            }

            // Add new roles
            if (rolesToAdd.Count > 0)
            {
                var addResult = await _userManager.AddToRolesAsync(user, rolesToAdd);
                if (addResult.Succeeded)
                {
                    _logger.LogInformation("Successfully added {Count} roles to user {UserId}", rolesToAdd.Count, user.Id);
                }
                else
                {
                    _logger.LogWarning("Failed to add roles to user {UserId}: {Errors}", user.Id,
                        string.Join(", ", addResult.Errors.Select(e => e.Description)));
                }
            }

            if (rolesToAdd.Count == 0 && rolesToRemove.Count == 0)
            {
                _logger.LogInformation("No role changes needed for user {UserId}", user.Id);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error syncing roles for user {UserId}", user.Id);
        }
    }
}
