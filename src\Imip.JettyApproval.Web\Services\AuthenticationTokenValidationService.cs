using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Volo.Abp.Guids;
using Volo.Abp.Identity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Security.Claims;
using Volo.Abp.Data;

namespace Imip.JettyApproval.Web.Services;

public class AuthenticationTokenValidationService : IAuthenticationTokenValidationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<AuthenticationTokenValidationService> _logger;
    private readonly IdentityUserManager _userManager;
    private readonly ICurrentTenant _currentTenant;
    private readonly IGuidGenerator _guidGenerator;

    public AuthenticationTokenValidationService(
        IConfiguration configuration,
        ILogger<AuthenticationTokenValidationService> logger,
        IdentityUserManager userManager,
        ICurrentTenant currentTenant,
        IGuidGenerator guidGenerator)
    {
        _configuration = configuration;
        _logger = logger;
        _userManager = userManager;
        _currentTenant = currentTenant;
        _guidGenerator = guidGenerator;
    }

    public async Task ValidateJwtBearerTokenAsync(Microsoft.AspNetCore.Authentication.JwtBearer.TokenValidatedContext context)
    {
        try
        {
            var claimsPrincipal = context.Principal;
            var email = GetEmailFromClaims(claimsPrincipal);
            var name = GetNameFromClaims(claimsPrincipal);
            var userName = GetUserNameFromClaims(claimsPrincipal);
            var sub = GetSubjectFromClaims(claimsPrincipal);

            if (string.IsNullOrEmpty(email))
            {
                _logger.LogWarning("JWT token validated but no email claim found");
                context.Fail("No email claim found in token");
                return;
            }

            // Find or create user in our system
            var user = await FindOrCreateUserAsync(email, name, userName, sub);
            if (user != null && claimsPrincipal.Identity is ClaimsIdentity identity)
            {
                // Add ABP-specific claims to the existing principal
                AddAbpClaims(identity, user);

                _logger.LogInformation("JWT token validated successfully for user: {Email}", email);
            }
            else
            {
                _logger.LogWarning("JWT token validated but user not found in local system: {Email}", email);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing JWT token validation");
            context.Fail("Token validation failed");
        }
    }

    public async Task ValidateOpenIdConnectTokenAsync(Microsoft.AspNetCore.Authentication.OpenIdConnect.TokenValidatedContext context)
    {
        try
        {
            var serviceProvider = context.HttpContext.RequestServices;
            var claimsPrincipal = context.Principal;

            // Extract claims BEFORE any transformations
            var email = GetEmailFromClaims(claimsPrincipal);
            var name = GetNameFromClaims(claimsPrincipal);
            var userName = GetUserNameFromClaims(claimsPrincipal);
            var sub = GetSubjectFromClaims(claimsPrincipal);

            // If we couldn't get the name from claims principal, try to get it from the token directly
            if (string.IsNullOrEmpty(name) || name == userName)
            {
                var givenName = claimsPrincipal?.Claims.FirstOrDefault(c => c.Type == "given_name")?.Value;
                if (!string.IsNullOrEmpty(givenName))
                {
                    _logger.LogInformation("Found given_name claim in principal: {GivenName}", givenName);
                    name = givenName;
                }
                else
                {
                    _logger.LogWarning("No given_name claim found in principal, checking all claims:");
                    foreach (var claim in claimsPrincipal?.Claims ?? Enumerable.Empty<Claim>())
                    {
                        _logger.LogWarning("Available claim: {Type} = {Value}", claim.Type, claim.Value);
                    }

                    // Try to get the name from the token directly
                    if (context.TokenEndpointResponse?.AccessToken != null)
                    {
                        try
                        {
                            var tokenHandler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
                            var token = tokenHandler.ReadJwtToken(context.TokenEndpointResponse.AccessToken);
                            var tokenGivenName = token.Claims.FirstOrDefault(c => c.Type == "given_name")?.Value;
                            if (!string.IsNullOrEmpty(tokenGivenName))
                            {
                                _logger.LogInformation("Found given_name claim in token: {GivenName}", tokenGivenName);
                                name = tokenGivenName;
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to parse token to extract given_name claim");
                        }
                    }
                }
            }

            _logger.LogInformation("OIDC Token Claims - Email: {Email}, Name: {Name}, UserName: {UserName}, Sub: {Sub}",
                email, name, userName, sub);

            if (string.IsNullOrEmpty(email))
            {
                _logger.LogWarning("OIDC token validated but no email claim found");
                context.Fail("No email claim found in token");
                return;
            }

            // Find or create user in our system
            var user = await FindOrCreateUserAsync(email, name, userName, sub);
            if (user != null && claimsPrincipal.Identity is ClaimsIdentity identity)
            {
                // Add ABP-specific claims to the existing principal
                AddAbpClaims(identity, user);

                // Store tokens for later use
                await StoreTokensAsync(context, user, email);

                _logger.LogInformation("OIDC token validated successfully for user: {Email}, Name: {Name}, UserName: {UserName}",
                    email, user.Name, user.UserName);
            }
            else
            {
                _logger.LogError("Failed to find or create user for email: {Email}", email);
                context.Fail("User creation failed");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing OIDC token validation");
            context.Fail("Token validation failed");
        }
    }

    private string? GetEmailFromClaims(ClaimsPrincipal? principal)
    {
        return principal?.FindFirst("email")?.Value ??
               principal?.FindFirst(ClaimTypes.Email)?.Value;
    }

    private string? GetNameFromClaims(ClaimsPrincipal? principal)
    {
        // Log all available claims for debugging
        if (principal != null)
        {
            _logger.LogInformation("Available claims for name extraction:");
            foreach (var claim in principal.Claims.Where(c => c.Type.Contains("name", StringComparison.OrdinalIgnoreCase) ||
                                                              c.Type.Contains("given", StringComparison.OrdinalIgnoreCase) ||
                                                              c.Type.Contains("family", StringComparison.OrdinalIgnoreCase)))
            {
                _logger.LogInformation("Claim: {Type} = {Value}", claim.Type, claim.Value);
            }

            // Also log all claims to see what's available
            _logger.LogInformation("All claims in principal:");
            foreach (var claim in principal.Claims)
            {
                _logger.LogInformation("All Claim: {Type} = {Value}", claim.Type, claim.Value);
            }
        }

        // Prioritize given_name (full name) over other name claims
        // Use FindAll to get all claims and then find the one we want
        var givenNameClaims = principal?.FindAll("given_name").ToList();
        _logger.LogInformation("Found {Count} given_name claims", givenNameClaims?.Count ?? 0);

        if (givenNameClaims != null && givenNameClaims.Any())
        {
            var givenName = givenNameClaims.First().Value;
            if (!string.IsNullOrEmpty(givenName))
            {
                _logger.LogInformation("Using given_name claim for name: {Name}", givenName);
                return givenName;
            }
        }

        // Try alternative approach - search through all claims manually
        var manualGivenName = principal?.Claims.FirstOrDefault(c => c.Type == "given_name")?.Value;
        if (!string.IsNullOrEmpty(manualGivenName))
        {
            _logger.LogInformation("Using manually found given_name claim for name: {Name}", manualGivenName);
            return manualGivenName;
        }

        // Fallback to name claim
        var nameClaims = principal?.FindAll("name").ToList();
        if (nameClaims != null && nameClaims.Any())
        {
            var name = nameClaims.First().Value;
            if (!string.IsNullOrEmpty(name))
            {
                _logger.LogInformation("Using name claim for name: {Name}", name);
                return name;
            }
        }

        // Fallback to family_name
        var familyNameClaims = principal?.FindAll("family_name").ToList();
        if (familyNameClaims != null && familyNameClaims.Any())
        {
            var familyName = familyNameClaims.First().Value;
            if (!string.IsNullOrEmpty(familyName))
            {
                _logger.LogInformation("Using family_name claim for name: {Name}", familyName);
                return familyName;
            }
        }

        // Last resort - ClaimTypes.Name (but be careful as this might contain username)
        var claimTypesNameClaims = principal?.FindAll(ClaimTypes.Name).ToList();
        if (claimTypesNameClaims != null && claimTypesNameClaims.Any())
        {
            var claimTypesName = claimTypesNameClaims.First().Value;
            if (!string.IsNullOrEmpty(claimTypesName))
            {
                _logger.LogInformation("Using ClaimTypes.Name claim for name: {Name}", claimTypesName);
                return claimTypesName;
            }
        }

        _logger.LogWarning("No suitable name claim found");
        return null;
    }

    private string? GetUserNameFromClaims(ClaimsPrincipal? principal)
    {
        return principal?.FindFirst("preferred_username")?.Value ??
               principal?.FindFirst("unique_name")?.Value ??
               principal?.FindFirst("username")?.Value;
    }

    private string? GetSubjectFromClaims(ClaimsPrincipal? principal)
    {
        return principal?.FindFirst("sub")?.Value ??
               principal?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }

    private void AddAbpClaims(ClaimsIdentity identity, IdentityUser user)
    {
        // Remove existing ABP claims to avoid duplicates
        var existingAbpClaims = identity.Claims
            .Where(c => c.Type == AbpClaimTypes.UserId ||
                       c.Type == AbpClaimTypes.UserName ||
                       c.Type == AbpClaimTypes.Email ||
                       c.Type == AbpClaimTypes.Name ||
                       c.Type == AbpClaimTypes.SurName ||
                       c.Type == AbpClaimTypes.TenantId ||
                       c.Type == ClaimTypes.Role)
            .ToList();

        foreach (var claim in existingAbpClaims)
        {
            identity.RemoveClaim(claim);
        }

        // Add ABP-specific claims
        identity.AddClaim(new Claim(AbpClaimTypes.UserId, user.Id.ToString()));
        identity.AddClaim(new Claim(AbpClaimTypes.UserName, user.UserName));
        identity.AddClaim(new Claim(AbpClaimTypes.Email, user.Email));

        if (!string.IsNullOrEmpty(user.Name))
        {
            identity.AddClaim(new Claim(AbpClaimTypes.Name, user.Name));
        }

        if (!string.IsNullOrEmpty(user.Surname))
        {
            identity.AddClaim(new Claim(AbpClaimTypes.SurName, user.Surname));
        }

        if (user.TenantId.HasValue)
        {
            identity.AddClaim(new Claim(AbpClaimTypes.TenantId, user.TenantId.Value.ToString()));
        }

        // Add roles from the original token claims
        var roleClaims = identity.Claims.Where(c => c.Type == "role").ToList();
        foreach (var roleClaim in roleClaims)
        {
            if (!identity.HasClaim(c => c.Type == ClaimTypes.Role && c.Value == roleClaim.Value))
            {
                identity.AddClaim(new Claim(ClaimTypes.Role, roleClaim.Value));
            }
        }
    }

    private async Task<IdentityUser?> FindOrCreateUserAsync(string email, string? name, string? userName, string? sub)
    {
        _logger.LogInformation("FindOrCreateUserAsync - Email: {Email}, Name: {Name}, UserName: {UserName}, Sub: {Sub}",
            email, name, userName, sub);

        // First try to find by external user ID (sub)
        var user = await _userManager.FindByIdAsync(sub);
        if (user != null)
        {
            _logger.LogInformation("Found existing user - Current Name: {CurrentName}, Current UserName: {CurrentUserName}",
                user.Name, user.UserName);

            // Update existing user information if needed
            var needsUpdate = false;

            if (!string.IsNullOrEmpty(name) && user.Name != name)
            {
                _logger.LogInformation("Updating user name from '{OldName}' to '{NewName}'", user.Name, name);
                user.Name = name;
                user.SetProperty("DisplayName", name);
                needsUpdate = true;
            }
            else if (!string.IsNullOrEmpty(name))
            {
                _logger.LogInformation("User name is already correct: '{Name}'", name);
            }
            else
            {
                _logger.LogWarning("No name provided for user update");
            }

            if (!string.IsNullOrEmpty(userName) && user.UserName != userName)
            {
                _logger.LogInformation("Updating user username from '{OldUserName}' to '{NewUserName}'", user.UserName, userName);
                await _userManager.SetUserNameAsync(user, userName);
                needsUpdate = true;
            }

            // Update external user ID if it changed
            var currentExternalId = user.GetProperty<string>("ExternalUserId");
            if (!string.IsNullOrEmpty(sub) && currentExternalId != sub)
            {
                user.SetProperty("ExternalUserId", sub);
                needsUpdate = true;
            }

            if (needsUpdate)
            {
                await _userManager.UpdateAsync(user);
                _logger.LogInformation("User updated successfully - Final Name: {Name}, UserName: {UserName}", user.Name, user.UserName);
            }
            else
            {
                _logger.LogInformation("No user updates needed - Current Name: {Name}, UserName: {UserName}", user.Name, user.UserName);
            }

            return user;
        }

        // If not found, create new user
        try
        {
            _logger.LogInformation("Creating new user for email: {Email}, external ID: {ExternalId}, name: {Name}, userName: {UserName}",
                email, sub, name, userName);

            // Parse userId string to Guid
            if (!string.IsNullOrEmpty(sub) && Guid.TryParse(sub, out var userGuid))
            {
                // Use the external user ID as the internal ID
                user = new IdentityUser(
                    userGuid,
                    userName ?? email,
                    email,
                    tenantId: _currentTenant.Id
                );
            }
            else
            {
                // Generate a new ID if external ID is not a valid GUID
                user = new IdentityUser(
                    _guidGenerator.Create(),
                    userName ?? email,
                    email,
                    tenantId: _currentTenant.Id
                );
            }

            if (!string.IsNullOrEmpty(name))
            {
                _logger.LogInformation("Setting user name to: {Name}", name);
                user.Name = name;
                user.SetProperty("DisplayName", name);
            }
            else
            {
                _logger.LogWarning("No name provided for new user");
            }

            // Store the external user ID for future reference
            if (!string.IsNullOrEmpty(sub))
            {
                user.SetProperty("ExternalUserId", sub);
            }

            user.SetEmailConfirmed(true); // Since it's from SSO, consider email verified

            var result = await _userManager.CreateAsync(user);
            if (result.Succeeded)
            {
                _logger.LogInformation("Created new user from SSO: {Email}, Name: {Name}, UserName: {UserName}",
                    email, user.Name, user.UserName);
                return user;
            }
            else
            {
                _logger.LogError("Failed to create user {Email}: {Errors}",
                    email, string.Join(", ", result.Errors.Select(e => e.Description)));
                return null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception creating user {Email}", email);
            return null;
        }
    }

    private async Task StoreTokensAsync(Microsoft.AspNetCore.Authentication.OpenIdConnect.TokenValidatedContext context, IdentityUser user, string email)
    {
        try
        {
            // Store tokens for later use
            if (context.Properties?.Items.ContainsKey(".Token.access_token") == true)
            {
                var accessToken = context.Properties.Items[".Token.access_token"];
                var refreshToken = context.Properties.Items.ContainsKey(".Token.refresh_token")
                    ? context.Properties.Items[".Token.refresh_token"]
                    : null;

                _logger.LogInformation("Access token stored for user: {Email}", email);

                // Store tokens in user properties for later use
                user.SetProperty("AccessToken", accessToken);
                if (!string.IsNullOrEmpty(refreshToken))
                {
                    user.SetProperty("RefreshToken", refreshToken);
                }

                // Update user with token information
                await _userManager.UpdateAsync(user);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing tokens for user: {Email}", email);
            // Don't fail the authentication if token storage fails
        }
    }
}
